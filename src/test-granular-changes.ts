/**
 * Test script to validate granular node changes functionality
 * This can be run in the browser console to test the changes
 */

import { NodeChange } from '@xyflow/react'
import { CustomNode } from '@/components/reactflow/node-types'

// Test data
const testNodes: CustomNode[] = [
  {
    id: 'node-1',
    type: 'input',
    position: { x: 100, y: 100 },
    data: { label: 'Test Node 1', description: 'Initial description' }
  },
  {
    id: 'node-2',
    type: 'default',
    position: { x: 200, y: 200 },
    data: { label: 'Test Node 2', description: 'Another description' }
  }
]

// Test individual node changes
export function testGranularChanges() {
  console.log('Testing granular node changes...')
  
  // Test 1: Position change
  const positionChange: NodeChange = {
    id: 'node-1',
    type: 'position',
    position: { x: 150, y: 150 }
  }
  
  console.log('Test 1 - Position change:', positionChange)
  
  // Test 2: Data update (replace)
  const dataChange: NodeChange = {
    id: 'node-1',
    type: 'replace',
    item: {
      ...testNodes[0],
      data: { 
        ...testNodes[0].data, 
        label: 'Updated Label',
        description: 'Updated description'
      }
    }
  }
  
  console.log('Test 2 - Data change:', dataChange)
  
  // Test 3: Node removal
  const removeChange: NodeChange = {
    id: 'node-2',
    type: 'remove'
  }
  
  console.log('Test 3 - Remove change:', removeChange)
  
  // Test 4: Node addition
  const addChange: NodeChange = {
    id: 'node-3',
    type: 'add',
    item: {
      id: 'node-3',
      type: 'output',
      position: { x: 300, y: 300 },
      data: { label: 'New Node', description: 'Newly added node' }
    }
  }
  
  console.log('Test 4 - Add change:', addChange)
  
  return {
    positionChange,
    dataChange,
    removeChange,
    addChange,
    testNodes
  }
}

// Test the new event structure
export function testEventStructure() {
  console.log('Testing new event structure...')
  
  const nodeChangesEvent = {
    type: 'node_changes',
    roomId: 'test-room',
    userId: 'test-user',
    data: {
      changes: [
        {
          id: 'node-1',
          type: 'position',
          position: { x: 150, y: 150 }
        }
      ]
    },
    timestamp: Date.now()
  }
  
  console.log('Node changes event:', nodeChangesEvent)
  
  const bulkNodesEvent = {
    type: 'nodes_change',
    roomId: 'test-room',
    userId: 'test-user',
    data: {
      nodes: testNodes
    },
    timestamp: Date.now()
  }
  
  console.log('Bulk nodes event:', bulkNodesEvent)
  
  return {
    nodeChangesEvent,
    bulkNodesEvent
  }
}

// Validation function to check if changes are properly structured
export function validateChangeStructure(changes: NodeChange[]) {
  console.log('Validating change structure...')
  
  const validTypes = ['add', 'remove', 'replace', 'position', 'dimensions', 'select']
  
  for (const change of changes) {
    if (!change.id) {
      console.error('Change missing id:', change)
      return false
    }
    
    if (!validTypes.includes(change.type)) {
      console.error('Invalid change type:', change.type)
      return false
    }
    
    // Validate specific change types
    switch (change.type) {
      case 'add':
      case 'replace':
        if (!change.item) {
          console.error('Add/Replace change missing item:', change)
          return false
        }
        break
      case 'position':
        if (!change.position) {
          console.error('Position change missing position:', change)
          return false
        }
        break
    }
  }
  
  console.log('All changes are valid!')
  return true
}

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testGranularChanges = testGranularChanges
  (window as any).testEventStructure = testEventStructure
  (window as any).validateChangeStructure = validateChangeStructure
}
